# Chunk Markers Feature

## Что это такое?

Для проверки качества перевода на стыках частей документа добавлена функция маркеров `*****`. Эти маркеры показывают, где заканчивается один чанк и начинается следующий.

## Как это работает?

1. **Во время перевода**: Текст разбивается на части (чанки) с сохранением границ предложений
2. **При склейке**: Между переведенными частями вставляются маркеры `*****`
3. **При скачивании**: Доступны две версии:
   - С маркерами (для проверки)
   - Без маркеров (чистая версия)

## Где найти маркеры в переведенном тексте?

Ищите строки вида:
```
...конец первой части.

*****

Начало следующей части...
```

## Как отключить маркеры?

Когда закончите тестирование, измените в файле `src/utils/constants.js`:

```javascript
// Было:
SHOW_CHUNK_MARKERS: true

// Станет:
SHOW_CHUNK_MARKERS: false
```

## Что проверять на стыках?

1. **Логическая связность**: Есть ли смысловые разрывы?
2. **Стилистическая согласованность**: Сохраняется ли стиль перевода?
3. **Терминология**: Переводятся ли одинаковые термины одинаково?
4. **Контекст**: Не теряется ли контекст между частями?

## Дополнительные функции

- **Автоматическое определение литературного контента**: Для художественных текстов используется специальный промпт
- **Умное разбиение**: Текст разбивается по границам предложений, а не по символам
- **Тестовый режим**: Обрабатываются только первые 3 части для быстрой проверки

## Файлы, связанные с маркерами

- `src/utils/constants.js` - настройка `SHOW_CHUNK_MARKERS`
- `src/services/textProcessor.js` - функция `reconstructText()`
- `src/utils/fileUtils.js` - функция `removeChunkMarkers()`
- `src/components/DownloadButton.jsx` - кнопки скачивания
