import { GEMINI_CONFIG, LITERARY_PATTERNS, FILE_CONFIG } from '../utils/constants.js';

/**
 * Detects if text appears to be literary/creative content
 * @param {string} text - Text to analyze
 * @returns {boolean} - True if text appears to be literary
 */
export const detectLiteraryContent = (text) => {
  const sampleText = text.slice(0, 2000).toLowerCase(); // Check first 2000 characters

  let literaryScore = 0;

  // Check for literary patterns
  LITERARY_PATTERNS.forEach(pattern => {
    if (pattern.test(sampleText)) {
      literaryScore++;
    }
  });

  // Check for dialogue patterns
  const dialogueMatches = sampleText.match(/"[^"]*"/g) || [];
  if (dialogueMatches.length > 3) {
    literaryScore += 2;
  }

  // Check for narrative indicators
  const narrativeWords = ['said', 'thought', 'felt', 'looked', 'walked', 'ran', 'whispered', 'shouted'];
  narrativeWords.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = sampleText.match(regex) || [];
    if (matches.length > 2) {
      literaryScore++;
    }
  });

  // Check for descriptive language
  const descriptiveWords = ['beautiful', 'mysterious', 'ancient', 'golden', 'silver', 'dark', 'bright', 'magnificent'];
  descriptiveWords.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    if (regex.test(sampleText)) {
      literaryScore++;
    }
  });

  return literaryScore >= 3;
};

/**
 * Splits text into chunks while preserving sentence boundaries
 * @param {string} text - Text to split
 * @param {number} maxChunkSize - Maximum size per chunk
 * @returns {Array<string>} - Array of text chunks
 */
export const splitTextIntoChunks = (text, maxChunkSize = GEMINI_CONFIG.TOKEN_LIMITS.CHUNK_SIZE) => {
  if (!text || text.trim().length === 0) {
    return [];
  }

  const chunks = [];
  let currentChunk = '';

  // Split by paragraphs first
  const paragraphs = text.split(/\n\s*\n/);

  for (const paragraph of paragraphs) {
    // If paragraph is too long, split by sentences
    if (paragraph.length > maxChunkSize) {
      const sentences = splitIntoSentences(paragraph);

      for (const sentence of sentences) {
        // If adding this sentence would exceed chunk size, start new chunk
        if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
          chunks.push(currentChunk.trim());
          currentChunk = sentence;
        } else {
          currentChunk += (currentChunk ? ' ' : '') + sentence;
        }

        // If single sentence is too long, force split
        if (currentChunk.length > maxChunkSize) {
          chunks.push(currentChunk.trim());
          currentChunk = '';
        }
      }
    } else {
      // If adding this paragraph would exceed chunk size, start new chunk
      if (currentChunk.length + paragraph.length > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = paragraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }
  }

  // Add remaining content
  if (currentChunk.trim().length > 0) {
    chunks.push(currentChunk.trim());
  }

  return chunks.filter(chunk => chunk.trim().length > 0);
};

/**
 * Splits text into sentences using multiple delimiters
 * @param {string} text - Text to split into sentences
 * @returns {Array<string>} - Array of sentences
 */
const splitIntoSentences = (text) => {
  // Enhanced sentence splitting that handles various punctuation
  const sentenceEnders = /[.!?]+/g;
  const sentences = [];
  let lastIndex = 0;
  let match;

  while ((match = sentenceEnders.exec(text)) !== null) {
    const endIndex = match.index + match[0].length;
    const sentence = text.slice(lastIndex, endIndex).trim();

    if (sentence.length > 0) {
      sentences.push(sentence);
    }

    lastIndex = endIndex;
  }

  // Add remaining text if any
  const remaining = text.slice(lastIndex).trim();
  if (remaining.length > 0) {
    sentences.push(remaining);
  }

  return sentences.filter(sentence => sentence.length > 0);
};

/**
 * Reconstructs translated text from chunks
 * @param {Array<string>} translatedChunks - Array of translated text chunks
 * @param {boolean} isLiterary - Whether content is literary
 * @param {boolean} showChunkMarkers - Whether to show chunk boundary markers for testing
 * @returns {string} - Reconstructed text
 */
export const reconstructText = (translatedChunks, isLiterary = false, showChunkMarkers = false) => {
  if (!translatedChunks || translatedChunks.length === 0) {
    return '';
  }

  // If showing chunk markers, add them between chunks
  if (showChunkMarkers) {
    const chunksWithMarkers = [];

    for (let i = 0; i < translatedChunks.length; i++) {
      chunksWithMarkers.push(translatedChunks[i].trim());

      // Add marker between chunks (not after the last one)
      if (i < translatedChunks.length - 1) {
        chunksWithMarkers.push('\n\n*****\n\n');
      }
    }

    return chunksWithMarkers.join('').replace(/\n{3,}/g, '\n\n');
  }

  // For literary content, preserve more spacing and structure
  if (isLiterary) {
    return translatedChunks
      .map(chunk => chunk.trim())
      .join('\n\n')
      .replace(/\n{3,}/g, '\n\n'); // Normalize excessive line breaks
  }

  // For regular content, join with appropriate spacing
  return translatedChunks
    .map(chunk => chunk.trim())
    .join('\n\n')
    .replace(/\n{3,}/g, '\n\n'); // Normalize excessive line breaks
};

/**
 * Estimates token count for text (rough approximation)
 * @param {string} text - Text to estimate
 * @returns {number} - Estimated token count
 */
export const estimateTokenCount = (text) => {
  // Rough estimation: 1 token ≈ 4 characters for English
  // This is a simplified estimation
  return Math.ceil(text.length / 4);
};

/**
 * Validates chunk size for API limits
 * @param {string} chunk - Text chunk to validate
 * @returns {boolean} - True if chunk is within limits
 */
export const validateChunkSize = (chunk) => {
  const estimatedTokens = estimateTokenCount(chunk);
  return estimatedTokens <= GEMINI_CONFIG.TOKEN_LIMITS.CHUNK_SIZE;
};

/**
 * Prepares chunks for testing mode (limits to first 3 chunks)
 * @param {Array<string>} chunks - All text chunks
 * @returns {Array<string>} - Limited chunks for testing
 */
export const prepareTestingChunks = (chunks) => {
  if (!FILE_CONFIG.TESTING_MODE) {
    return chunks;
  }

  return chunks.slice(0, FILE_CONFIG.MAX_CHUNKS_TESTING);
};
