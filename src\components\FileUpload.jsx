import React, { useState, useRef } from 'react';
import { Upload, File, X, AlertCircle } from 'lucide-react';
import { validateFile, readFileAsText, formatFileSize, countWords, countCharacters } from '../utils/fileUtils.js';

const FileUpload = ({ onFileSelect, disabled }) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileStats, setFileStats] = useState(null);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);

  const handleFileSelection = async (file) => {
    setError(null);

    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      setError(validation.error);
      return;
    }

    try {
      // Read file content
      const content = await readFileAsText(file);

      // Calculate stats
      const stats = {
        size: formatFileSize(file.size),
        words: countWords(content),
        characters: countCharacters(content),
        name: file.name
      };

      setSelectedFile(file);
      setFileStats(stats);

      // Pass file and content to parent
      onFileSelect({
        file,
        content,
        stats
      });

    } catch (error) {
      setError(`Failed to read file: ${error.message}`);
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileInputChange = (e) => {
    if (disabled) return;

    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelection(files[0]);
      // Reset input to allow selecting the same file again if needed
      e.target.value = '';
    }
  };

  const handleClick = () => {
    if (disabled) return;
    fileInputRef.current?.click();
  };

  const clearFile = () => {
    setSelectedFile(null);
    setFileStats(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onFileSelect(null);
  };

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium mb-3" style={{
        color: 'var(--text-secondary)',
        fontFamily: "'Crimson Text', serif",
        fontVariant: 'small-caps',
        fontSize: '1.1rem',
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
      }}>
        📜 Upload Ancient Text Document
      </label>

      {!selectedFile ? (
        <div
          className={`
            file-upload-area relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-300
            ${dragActive ? 'border-red-500 bg-red-900/20' : 'border-gray-600 hover:border-red-600'}
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          style={{
            background: dragActive
              ? 'linear-gradient(145deg, rgba(139, 0, 0, 0.1), rgba(255, 107, 53, 0.1))'
              : 'linear-gradient(145deg, var(--background-secondary), var(--surface-darker))',
            boxShadow: dragActive
              ? '0 0 20px rgba(139, 0, 0, 0.4), inset 0 2px 4px rgba(0, 0, 0, 0.6)'
              : 'inset 0 2px 4px rgba(0, 0, 0, 0.6)'
          }}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".txt"
            onChange={handleFileInputChange}
            className="hidden"
            disabled={disabled}
          />

          <Upload className="mx-auto h-12 w-12 text-red-400 mb-4"
                  style={{
                    filter: 'drop-shadow(0 0 8px rgba(139, 0, 0, 0.6))',
                    animation: dragActive ? 'pulse 1s infinite' : 'none'
                  }} />

          <div className="space-y-2">
            <p className="text-lg font-medium" style={{
              color: 'var(--text-primary)',
              fontFamily: "'Crimson Text', serif",
              fontVariant: 'small-caps',
              textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
            }}>
              Drop your ancient scroll here, or click to browse the archives
            </p>
            <p className="text-sm" style={{
              color: 'var(--text-muted)',
              fontFamily: "'Crimson Text', serif",
              fontVariant: 'small-caps'
            }}>
              Only .txt scrolls are accepted (max 50MB of ancient wisdom)
            </p>
          </div>
        </div>
      ) : (
        <div className="border-2 border-yellow-600/50 rounded-lg p-4" style={{
          background: 'linear-gradient(145deg, var(--background-secondary), var(--surface-darker))',
          boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.6), 0 0 15px rgba(212, 175, 55, 0.2)'
        }}>
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <File className="h-8 w-8 text-yellow-500" style={{
                filter: 'drop-shadow(0 0 6px rgba(212, 175, 55, 0.6))'
              }} />
              <div>
                <p className="font-medium" style={{
                  color: 'var(--text-primary)',
                  fontFamily: "'Crimson Text', serif",
                  fontVariant: 'small-caps',
                  fontSize: '1.1rem'
                }}>{fileStats?.name}</p>
                <div className="text-sm space-y-1" style={{
                  color: 'var(--text-secondary)',
                  fontFamily: "'Crimson Text', serif",
                  fontVariant: 'small-caps'
                }}>
                  <p>📏 Size: {fileStats?.size}</p>
                  <p>📝 Words: {fileStats?.words?.toLocaleString()}</p>
                  <p>🔤 Characters: {fileStats?.characters?.toLocaleString()}</p>
                </div>
              </div>
            </div>

            <button
              onClick={clearFile}
              className="text-red-400 hover:text-red-300 transition-colors"
              disabled={disabled}
              style={{ filter: 'drop-shadow(0 0 4px rgba(139, 0, 0, 0.6))' }}
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      {error && (
        <div className="mt-3 flex items-center space-x-2" style={{ color: 'var(--error-color)' }}>
          <AlertCircle className="h-4 w-4" style={{ filter: 'drop-shadow(0 0 4px rgba(220, 20, 60, 0.6))' }} />
          <span className="text-sm" style={{
            fontFamily: "'Crimson Text', serif",
            fontVariant: 'small-caps',
            textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
          }}>{error}</span>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
