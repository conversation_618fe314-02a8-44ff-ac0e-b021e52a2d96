:root {
  font-family: '<PERSON><PERSON><PERSON>', 'Crimson Text', serif;
  line-height: 1.6;
  font-weight: 400;

  color-scheme: dark;
  color: #d4af37;
  background-color: #0a0a0a;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  /* Diablo-inspired CSS Variables */
  --primary-color: #8b0000; /* Dark red */
  --primary-hover: #a50000;
  --secondary-color: #d4af37; /* Gold */
  --accent-color: #ff6b35; /* Orange-red */
  --success-color: #228b22;
  --warning-color: #ffa500;
  --error-color: #dc143c;

  /* Backgrounds */
  --background-primary: #0a0a0a; /* Deep black */
  --background-secondary: #1a1a1a; /* Dark gray */
  --background-tertiary: #2a2a2a; /* Medium gray */
  --surface-dark: #1e1e1e;
  --surface-darker: #141414;

  /* Text colors */
  --text-primary: #d4af37; /* Gold */
  --text-secondary: #c0c0c0; /* Silver */
  --text-muted: #808080; /* <PERSON> */
  --text-accent: #ff6b35; /* Orange-red */

  /* Borders and effects */
  --border-primary: #8b0000;
  --border-secondary: #d4af37;
  --border-muted: #444444;

  /* Shadows with red glow */
  --shadow-red: 0 0 10px rgba(139, 0, 0, 0.5);
  --shadow-gold: 0 0 15px rgba(212, 175, 55, 0.3);
  --shadow-dark: 0 4px 20px rgba(0, 0, 0, 0.8);
  --shadow-inset: inset 0 2px 4px rgba(0, 0, 0, 0.6);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, rgba(139, 0, 0, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  background-attachment: fixed;
}

#root {
  width: 100%;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.card {
  background:
    linear-gradient(145deg, var(--background-secondary) 0%, var(--surface-darker) 100%);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 2rem;
  box-shadow:
    var(--shadow-dark),
    inset 0 1px 0 rgba(212, 175, 55, 0.1),
    0 0 20px rgba(139, 0, 0, 0.2);
  border: 2px solid var(--border-primary);
  border-image: linear-gradient(45deg, var(--border-primary), var(--border-secondary), var(--border-primary)) 1;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--secondary-color), transparent);
  opacity: 0.6;
}

.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 10%, rgba(212, 175, 55, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 90%, rgba(139, 0, 0, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  border: 2px solid var(--border-primary);
  border-radius: 4px;
  font-family: 'Cinzel', serif;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  text-transform: uppercase;
  min-height: 48px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(145deg, var(--background-tertiary), var(--surface-darker));
  color: var(--text-primary);
  box-shadow:
    var(--shadow-inset),
    0 0 10px rgba(139, 0, 0, 0.3);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(145deg, var(--primary-color), #660000);
  border-color: var(--primary-color);
  color: var(--text-primary);
  box-shadow:
    var(--shadow-inset),
    var(--shadow-red);
}

.btn-primary:hover {
  background: linear-gradient(145deg, var(--primary-hover), var(--primary-color));
  transform: translateY(-2px);
  box-shadow:
    var(--shadow-inset),
    0 0 20px rgba(139, 0, 0, 0.6),
    0 4px 15px rgba(0, 0, 0, 0.4);
}

.btn-secondary {
  background: linear-gradient(145deg, var(--secondary-color), #b8941f);
  border-color: var(--secondary-color);
  color: var(--background-primary);
  box-shadow:
    var(--shadow-inset),
    var(--shadow-gold);
}

.btn-secondary:hover {
  background: linear-gradient(145deg, #f4c430, var(--secondary-color));
  transform: translateY(-2px);
  box-shadow:
    var(--shadow-inset),
    0 0 20px rgba(212, 175, 55, 0.6),
    0 4px 15px rgba(0, 0, 0, 0.4);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-muted);
  border-radius: 4px;
  font-family: 'Crimson Text', serif;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: linear-gradient(145deg, var(--surface-darker), var(--background-secondary));
  color: var(--text-secondary);
  box-shadow: var(--shadow-inset);
}

.input:focus {
  outline: none;
  border-color: var(--border-secondary);
  box-shadow:
    var(--shadow-inset),
    0 0 10px rgba(212, 175, 55, 0.3);
  color: var(--text-primary);
}

.input::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

.select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-muted);
  border-radius: 4px;
  font-family: 'Crimson Text', serif;
  font-size: 0.9rem;
  background: linear-gradient(145deg, var(--surface-darker), var(--background-secondary));
  color: var(--text-secondary);
  cursor: pointer;
  box-shadow: var(--shadow-inset);
  transition: all 0.3s ease;
}

.select:focus {
  outline: none;
  border-color: var(--border-secondary);
  box-shadow:
    var(--shadow-inset),
    0 0 10px rgba(212, 175, 55, 0.3);
  color: var(--text-primary);
}

.select option {
  background: var(--background-secondary);
  color: var(--text-secondary);
  padding: 0.5rem;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.grid {
  display: grid;
  gap: 1rem;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  #root {
    padding: 1rem;
  }

  .card {
    padding: 1.5rem;
  }
}
