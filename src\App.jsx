import React, { useState, useEffect } from 'react';
import { Globe, Sparkles, AlertTriangle, Flame, Skull } from 'lucide-react';
import FileUpload from './components/FileUpload';
import LanguageSelector from './components/LanguageSelector';
import ApiKeyInput from './components/ApiKeyInput';
import TranslationProgress from './components/TranslationProgress';
import DownloadButton from './components/DownloadButton';
import { translationService } from './services/translationService';
import { getRateLimiter } from './services/geminiApi';
import { LANGUAGES } from './utils/constants';
import './App.css';

function App() {
  // Form state
  const [fileData, setFileData] = useState(null);
  const [sourceLanguage, setSourceLanguage] = useState('');
  const [targetLanguage, setTargetLanguage] = useState('');
  const [apiKey, setApiKey] = useState('');

  // Translation state
  const [isTranslating, setIsTranslating] = useState(false);
  const [progress, setProgress] = useState(null);
  const [status, setStatus] = useState('');
  const [translatedText, setTranslatedText] = useState('');
  const [translationResult, setTranslationResult] = useState(null);
  const [error, setError] = useState('');

  // Rate limiting state
  const [rateLimitWarning, setRateLimitWarning] = useState('');

  // Load saved API key on mount
  useEffect(() => {
    const savedApiKey = localStorage.getItem('gemini_api_key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
    }
  }, []);

  // Save API key to localStorage
  useEffect(() => {
    if (apiKey.trim()) {
      localStorage.setItem('gemini_api_key', apiKey);
    }
  }, [apiKey]);

  // Check rate limits periodically
  useEffect(() => {
    const checkRateLimits = () => {
      const rateLimiter = getRateLimiter();
      const warning = rateLimiter.getWarning();
      setRateLimitWarning(warning || '');
    };

    checkRateLimits();
    const interval = setInterval(checkRateLimits, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Set up translation service callbacks
  useEffect(() => {
    translationService.setProgressCallback(setProgress);
    translationService.setStatusCallback(setStatus);
  }, []);

  const handleFileSelect = (data) => {
    setFileData(data);
    setError('');
    setTranslatedText('');
    setTranslationResult(null);
  };

  const handleTranslate = async () => {
    if (!fileData || !sourceLanguage || !targetLanguage || !apiKey) {
      setError('Please fill in all required fields');
      return;
    }

    setError('');
    setTranslatedText('');
    setTranslationResult(null);
    setIsTranslating(true);

    try {
      const result = await translationService.translateText({
        text: fileData.content,
        sourceLanguage,
        targetLanguage,
        apiKey
      });

      setTranslatedText(result.translatedText);
      setTranslationResult(result);
      setStatus('Translation completed successfully!');

    } catch (error) {
      console.error('Translation error:', error);
      setError(error.message);
      setStatus('Translation failed');
    } finally {
      setIsTranslating(false);
    }
  };

  const handleCancel = () => {
    translationService.cancelTranslation();
    setIsTranslating(false);
  };

  const canTranslate = fileData && sourceLanguage && targetLanguage && apiKey && !isTranslating;

  const getSourceLanguageName = () => {
    return LANGUAGES.find(l => l.code === sourceLanguage)?.name || sourceLanguage;
  };

  const getTargetLanguageName = () => {
    return LANGUAGES.find(l => l.code === targetLanguage)?.name || targetLanguage;
  };

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-4 mb-6">
            <Flame className="h-10 w-10 text-red-600" style={{ filter: 'drop-shadow(0 0 8px rgba(139, 0, 0, 0.8))' }} />
            <h1 className="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-600 via-yellow-500 to-red-600"
                style={{
                  fontFamily: "'Nosifer', 'Uncial Antiqua', cursive",
                  textTransform: 'uppercase',
                  letterSpacing: '3px',
                  textShadow: '3px 3px 6px rgba(0, 0, 0, 0.9), 0 0 15px rgba(139, 0, 0, 0.7)',
                  filter: 'drop-shadow(0 0 8px rgba(212, 175, 55, 0.5))'
                }}>
              Tyrael
            </h1>
            <Skull className="h-10 w-10 text-yellow-500" style={{ filter: 'drop-shadow(0 0 8px rgba(212, 175, 55, 0.8))' }} />
          </div>
          <div className="max-w-3xl mx-auto">
            <p style={{
              fontFamily: "'MedievalSharp', 'Crimson Text', serif",
              textTransform: 'uppercase',
              letterSpacing: '1px',
              color: 'var(--text-primary)',
              textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)',
              lineHeight: '1.4',
              fontSize: '1.4rem',
              marginBottom: '1rem'
            }}>
              <span className="text-red-400 font-bold">Large Document Translation</span> powered by the dark arts of <span className="text-yellow-400 font-semibold">Google Gemini 2.5 Flash</span> AI.
            </p>
            <p style={{
              fontFamily: "'MedievalSharp', 'Crimson Text', serif",
              textTransform: 'uppercase',
              letterSpacing: '0.8px',
              color: 'var(--text-secondary)',
              textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)',
              fontSize: '1.1rem'
            }}>
              Intelligent chunking preserves the essence and structure of texts.
            </p>
          </div>
        </div>

        {/* Rate Limit Warning */}
        {rateLimitWarning && (
          <div className="mb-6 p-4 bg-gradient-to-r from-red-900/20 to-orange-900/20 border-2 border-red-600/50 rounded-lg backdrop-blur-sm">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-400" style={{ filter: 'drop-shadow(0 0 4px rgba(255, 165, 0, 0.6))' }} />
              <span className="text-orange-300 font-medium" style={{ fontFamily: "'Crimson Text', serif" }}>{rateLimitWarning}</span>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          <div className="card">
            {/* File Upload */}
            <FileUpload
              onFileSelect={handleFileSelect}
              disabled={isTranslating}
            />

            {/* Language Selection */}
            <LanguageSelector
              sourceLanguage={sourceLanguage}
              targetLanguage={targetLanguage}
              onSourceLanguageChange={setSourceLanguage}
              onTargetLanguageChange={setTargetLanguage}
              disabled={isTranslating}
            />

            {/* API Key Input */}
            <ApiKeyInput
              apiKey={apiKey}
              onApiKeyChange={setApiKey}
              disabled={isTranslating}
            />

            {/* Error Display */}
            {error && (
              <div className="mb-6 p-4 bg-gradient-to-r from-red-900/30 to-red-800/30 border-2 border-red-500 rounded-lg backdrop-blur-sm">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-400" style={{ filter: 'drop-shadow(0 0 6px rgba(220, 20, 60, 0.8))' }} />
                  <span className="text-red-300 font-medium" style={{ fontFamily: "'Crimson Text', serif" }}>{error}</span>
                </div>
              </div>
            )}

            {/* Translate Button - Diablo Style */}
            <div className="mb-6 flex justify-center">
              {/* Outer metallic frame */}
              <div style={{
                background: 'linear-gradient(145deg, #d4af37, #b8941f, #8b7355)',
                padding: '4px',
                borderRadius: '2px',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.6)',
                width: 'fit-content'
              }}>
                {/* Inner frame */}
                <div style={{
                  background: 'linear-gradient(145deg, #8b7355, #6b5b47)',
                  padding: '2px',
                  borderRadius: '1px'
                }}>
                  {/* Red button center */}
                  <button
                    onClick={handleTranslate}
                    disabled={!canTranslate}
                    className="py-3 px-8 relative transition-all duration-200"
                    style={{
                      fontFamily: "'MedievalSharp', 'Cinzel', serif",
                      textTransform: 'uppercase',
                      letterSpacing: '2px',
                      fontSize: '0.95rem',
                      fontWeight: '600',
                      background: 'linear-gradient(180deg, #8b0000, #660000, #4d0000)',
                      border: 'none',
                      borderRadius: '1px',
                      color: '#d4af37',
                      textShadow: '1px 1px 3px rgba(0, 0, 0, 0.9)',
                      boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.5)',
                      cursor: !canTranslate ? 'not-allowed' : 'pointer',
                      opacity: !canTranslate ? 0.6 : 1,
                      minHeight: '48px'
                    }}
                    onMouseEnter={(e) => {
                      if (canTranslate) {
                        e.target.style.background = 'linear-gradient(180deg, #a50000, #8b0000, #660000)';
                        e.target.style.color = '#f4e4bc';
                        e.target.style.boxShadow = 'inset 0 2px 4px rgba(0, 0, 0, 0.5), 0 0 8px rgba(139, 0, 0, 0.4)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (canTranslate) {
                        e.target.style.background = 'linear-gradient(180deg, #8b0000, #660000, #4d0000)';
                        e.target.style.color = '#d4af37';
                        e.target.style.boxShadow = 'inset 0 2px 4px rgba(0, 0, 0, 0.5)';
                      }
                    }}
                  >
                    {isTranslating ? (
                      <div className="flex items-center justify-center space-x-3">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-transparent border-t-current border-r-current"
                             style={{ filter: 'drop-shadow(0 0 4px currentColor)' }}></div>
                        <span>Translating...</span>
                      </div>
                    ) : (
                      <div className="text-center">
                        <div>Translate Document</div>
                        {sourceLanguage && targetLanguage && (
                          <div className="text-xs opacity-90 mt-1" style={{
                            letterSpacing: '1px',
                            fontSize: '0.7rem'
                          }}>
                            {getSourceLanguageName()} → {getTargetLanguageName()}
                          </div>
                        )}
                      </div>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Translation Progress */}
            <TranslationProgress
              isTranslating={isTranslating}
              progress={progress}
              status={status}
              onCancel={handleCancel}
              eta={progress?.eta}
            />

            {/* Download Button */}
            <DownloadButton
              translatedText={translatedText}
              originalFilename={fileData?.file?.name}
              targetLanguage={targetLanguage}
              translationResult={translationResult}
              disabled={isTranslating}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-12">
          <p className="text-sm" style={{
            fontFamily: "'MedievalSharp', 'Crimson Text', serif",
            textTransform: 'uppercase',
            letterSpacing: '0.6px',
            fontSize: '0.9rem',
            color: 'var(--text-secondary)',
            textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
          }}>
            <span className="text-red-400">Powered by the dark magic of Google Gemini 2.5 Flash</span> • <span className="text-yellow-400">Forged with React & Vite</span>
          </p>
          <p className="text-xs mt-2" style={{
            fontFamily: "'MedievalSharp', 'Crimson Text', serif",
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            fontSize: '0.8rem',
            color: 'var(--text-muted)',
            textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
          }}>
            Your arcane secrets (API key) are bound to your local realm and never traverse our domains
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
