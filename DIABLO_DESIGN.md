# 🔥 Diablo-Inspired Design Theme

## 🎨 Design Overview

Приложение теперь имеет темную, мистическую тему в стиле игры Diablo с готическими элементами и атмосферой древних магических артефактов.

## 🎭 Цветовая палитра

### **Основные цвета:**
- **Темно-красный** (`#8b0000`) - основной цвет кнопок и акцентов
- **Золотой** (`#d4af37`) - вторичный цвет, текст и границы
- **Оранжево-красный** (`#ff6b35`) - акцентный цвет для эффектов
- **Глубокий черный** (`#0a0a0a`) - основной фон
- **Темно-серый** (`#1a1a1a`) - вторичный фон
- **Серебристый** (`#c0c0c0`) - вторичный текст

### **Эффекты:**
- **Красное свечение** - для кнопок и активных элементов
- **Золотое свечение** - для фокуса и выделения
- **Темные тени** - для глубины и объема

## 🔤 Типографика

### **Шрифты:**
1. **Uncial Antiqua** - заголовок (готический стиль)
2. **Cinzel** - заголовки и кнопки (элегантный serif)
3. **Crimson Text** - основной текст и лейблы (читаемый serif)

### **Эффекты текста:**
- Тени для всех текстовых элементов
- Градиенты для заголовка
- Свечение для иконок

## 🎯 Ключевые элементы дизайна

### **Карточки:**
- Градиентный фон (темно-серый → черный)
- Красные границы с градиентом
- Внутренние тени для глубины
- Тонкие световые линии сверху

### **Кнопки:**
- Градиентные фоны
- Анимация свечения при наведении
- Эффект "блеска" при взаимодействии
- Приподнимание при hover

### **Поля ввода:**
- Темный градиентный фон
- Золотое свечение при фокусе
- Внутренние тени
- Стилизованные placeholder'ы

### **Фон:**
- Многослойные радиальные градиенты
- Красные и золотые световые пятна
- Фиксированное крепление для эффекта глубины

## 🔮 Мистические элементы

### **Иконки и эмодзи:**
- 🔥 Flame - в заголовке
- 💀 Skull - в заголовке  
- 📜 Scroll - для загрузки файлов
- 📏📝🔤 - для статистики файлов

### **Текстовые элементы:**
- "Arcane Document Translation"
- "Ancient Text Document"
- "Drop your ancient scroll here"
- "Dark arts of Google Gemini AI"
- "Arcane secrets (API key)"

### **Анимации:**
- Пульсация для активных элементов
- Блеск на кнопках
- Свечение при наведении
- Плавные переходы

## 🛠️ Технические детали

### **CSS переменные:**
```css
--primary-color: #8b0000;      /* Dark red */
--secondary-color: #d4af37;    /* Gold */
--accent-color: #ff6b35;       /* Orange-red */
--background-primary: #0a0a0a; /* Deep black */
--text-primary: #d4af37;       /* Gold text */
```

### **Ключевые классы:**
- `.card` - основные контейнеры
- `.btn-primary` - красные кнопки
- `.btn-secondary` - золотые кнопки
- `.file-upload-area` - зона загрузки файлов

### **Эффекты:**
- `drop-shadow()` для свечения иконок
- `text-shadow` для всех текстов
- `box-shadow` с цветными тенями
- `linear-gradient()` для фонов и границ

## 🎮 Атмосфера

Дизайн создает ощущение:
- **Древности и мистики**
- **Мощи и магии**
- **Профессионализма с готическим оттенком**
- **Интерактивности и отзывчивости**

Приложение теперь выглядит как инструмент для перевода древних магических текстов! 🧙‍♂️✨
