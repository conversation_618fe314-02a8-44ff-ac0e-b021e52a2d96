import React from 'react';
import { Languages, ArrowRight } from 'lucide-react';
import { LANGUAGES } from '../utils/constants.js';

const LanguageSelector = ({
  sourceLanguage,
  targetLanguage,
  onSourceLanguageChange,
  onTargetLanguageChange,
  disabled
}) => {
  return (
    <div className="mb-6">
      <label className="block text-sm font-medium mb-3" style={{
        color: 'var(--text-secondary)',
        fontFamily: "'MedievalSharp', 'Crimson Text', serif",
        textTransform: 'uppercase',
        letterSpacing: '0.6px',
        fontSize: '1rem',
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
      }}>
        <Languages className="inline h-4 w-4 mr-2" />
        Translation Languages
      </label>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
        {/* Source Language */}
        <div>
          <label className="block text-xs font-medium mb-1" style={{
            color: 'var(--text-muted)',
            fontFamily: "'MedievalSharp', 'Crimson Text', serif",
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
          }}>
            From
          </label>
          <select
            value={sourceLanguage}
            onChange={(e) => onSourceLanguageChange(e.target.value)}
            className="select"
            disabled={disabled}
          >
            <option value="">Select source language</option>
            {LANGUAGES.map((lang) => (
              <option key={lang.code} value={lang.code}>
                {lang.name}
              </option>
            ))}
          </select>
        </div>

        {/* Arrow */}
        <div className="flex justify-center">
          <ArrowRight className="h-6 w-6 text-gray-400" />
        </div>

        {/* Target Language */}
        <div>
          <label className="block text-xs font-medium mb-1" style={{
            color: 'var(--text-muted)',
            fontFamily: "'MedievalSharp', 'Crimson Text', serif",
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
          }}>
            To
          </label>
          <select
            value={targetLanguage}
            onChange={(e) => onTargetLanguageChange(e.target.value)}
            className="select"
            disabled={disabled}
          >
            <option value="">Select target language</option>
            {LANGUAGES.filter(lang => lang.code !== 'auto' && lang.code !== sourceLanguage).map((lang) => (
              <option key={lang.code} value={lang.code}>
                {lang.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Language pair display */}
      {sourceLanguage && targetLanguage && (
        <div className="mt-3 p-3 rounded-lg border-2" style={{
          background: 'linear-gradient(145deg, rgba(139, 0, 0, 0.1), rgba(255, 107, 53, 0.05))',
          borderColor: 'var(--border-primary)',
          boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.6), 0 0 10px rgba(139, 0, 0, 0.2)'
        }}>
          <p className="text-sm" style={{
            color: 'var(--text-primary)',
            fontFamily: 'var(--font-diablo)',
            fontVariant: 'small-caps',
            textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
          }}>
            <span className="font-medium">Translation:</span>{' '}
            {LANGUAGES.find(l => l.code === sourceLanguage)?.name} → {LANGUAGES.find(l => l.code === targetLanguage)?.name}
          </p>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
