import React from 'react';
import { Loader2, CheckCircle, AlertCircle, Clock, Zap } from 'lucide-react';

const TranslationProgress = ({
  isTranslating,
  progress,
  status,
  onCancel,
  eta
}) => {
  if (!isTranslating && !progress) {
    return null;
  }

  const { current = 0, total = 0, percentage = 0 } = progress || {};

  return (
    <div className="mb-6">
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {isTranslating ? (
              <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
            ) : percentage === 100 ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-amber-500" />
            )}

            <h3 className="text-lg font-medium" style={{
              color: 'var(--text-primary)',
              fontFamily: 'var(--font-diablo)',
              textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
            }}>
              {isTranslating ? 'Translating Document' : 'Translation Status'}
            </h3>
          </div>

          {isTranslating && onCancel && (
            <button
              onClick={onCancel}
              className="text-sm font-medium"
              style={{
                color: 'var(--error-color)',
                fontFamily: "'Crimson Text', serif",
                textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
              }}
            >
              Cancel
            </button>
          )}
        </div>

        {/* Progress Bar */}
        {total > 0 && (
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress: {current} of {total} chunks</span>
              <span>{percentage}%</span>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`
                  h-2 rounded-full transition-all duration-300 ease-out
                  ${isTranslating ? 'bg-blue-500' : percentage === 100 ? 'bg-green-500' : 'bg-amber-500'}
                `}
                style={{ width: `${percentage}%` }}
              />
            </div>
          </div>
        )}

        {/* Status Message */}
        {status && (
          <div className="mb-4">
            <p className="text-sm" style={{
              color: 'var(--text-secondary)',
              fontFamily: 'var(--font-diablo)',
              textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
            }}>{status}</p>
          </div>
        )}

        {/* Additional Info */}
        {isTranslating && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {/* Estimated Time */}
            {eta && (
              <div className="flex items-center space-x-2 text-gray-600">
                <Clock className="h-4 w-4" />
                <span>ETA: {eta}</span>
              </div>
            )}

            {/* Rate Limit Info */}
            <div className="flex items-center space-x-2 text-gray-600">
              <Zap className="h-4 w-4" />
              <span>Rate limited: 6s between requests</span>
            </div>
          </div>
        )}

        {/* Completion Message */}
        {!isTranslating && percentage === 100 && (
          <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                Translation completed successfully!
              </span>
            </div>
          </div>
        )}

        {/* Error State */}
        {!isTranslating && percentage > 0 && percentage < 100 && (
          <div className="mt-4 p-3 bg-amber-50 rounded-lg border border-amber-200">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-amber-600" />
              <span className="text-sm font-medium text-amber-800">
                Translation incomplete - check status messages above
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TranslationProgress;
