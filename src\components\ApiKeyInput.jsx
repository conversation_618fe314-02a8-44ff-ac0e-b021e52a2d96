import React, { useState } from 'react';
import { Key, Eye, EyeOff, AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';
import { validateApiKey } from '../services/geminiApi.js';

const ApiKeyInput = ({ api<PERSON>ey, onApi<PERSON>eyChange, disabled }) => {
  const [showKey, setShowKey] = useState(false);
  const [isValid, setIsValid] = useState(null);

  const handleKeyChange = (e) => {
    const key = e.target.value;
    onApiKeyChange(key);

    // Validate key format
    if (key.trim().length === 0) {
      setIsValid(null);
    } else {
      setIsValid(validateApiKey(key));
    }
  };

  const toggleShowKey = () => {
    setShowKey(!showKey);
  };

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium mb-2" style={{
        color: 'var(--text-secondary)',
        fontFamily: "'Crimson Text', serif",
        fontSize: '1.1rem',
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
      }}>
        <Key className="inline h-4 w-4 mr-2" />
        Gemini API Key
      </label>

      <div className="relative">
        <input
          type={showKey ? 'text' : 'password'}
          value={apiKey}
          onChange={handleKeyChange}
          placeholder="Enter your Gemini API key (AIza...)"
          className={`
            input pr-20
            ${isValid === true ? 'border-green-500 focus:border-green-500' : ''}
            ${isValid === false ? 'border-red-500 focus:border-red-500' : ''}
          `}
          disabled={disabled}
        />

        <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
          {/* Validation indicator */}
          {isValid === true && (
            <CheckCircle className="h-4 w-4 text-green-500" />
          )}
          {isValid === false && (
            <AlertCircle className="h-4 w-4 text-red-500" />
          )}

          {/* Show/hide toggle */}
          <button
            type="button"
            onClick={toggleShowKey}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={disabled}
          >
            {showKey ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        </div>
      </div>

      {/* Validation message */}
      {isValid === false && (
        <div className="mt-2 flex items-center space-x-2" style={{ color: 'var(--error-color)' }}>
          <AlertCircle className="h-4 w-4" style={{ filter: 'drop-shadow(0 0 4px rgba(220, 20, 60, 0.6))' }} />
          <span className="text-sm" style={{
            fontFamily: "'Crimson Text', serif",
            textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
          }}>Invalid API key format</span>
        </div>
      )}

      {/* Help text */}
      <div className="mt-2 text-sm" style={{
        color: 'var(--text-muted)',
        fontFamily: 'var(--font-diablo)',
        textTransform: 'uppercase',
        fontSize: '0.85rem',
        letterSpacing: '0.5px',
        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
      }}>
        <p>
          Get your free API key from{' '}
          <a
            href="https://aistudio.google.com/app/apikey"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center"
            style={{
              color: 'var(--text-accent)',
              textDecoration: 'underline',
              fontFamily: 'var(--font-diablo)',
              textTransform: 'uppercase',
              fontSize: '0.85rem',
              letterSpacing: '0.5px'
            }}
          >
            Google AI Studio
            <ExternalLink className="h-3 w-3 ml-1" />
          </a>
        </p>
        <p className="mt-1" style={{
          fontFamily: 'var(--font-diablo)',
          textTransform: 'uppercase',
          fontSize: '0.85rem',
          letterSpacing: '0.5px',
          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
        }}>
          Free tier includes: 10 requests/minute, 250K tokens/minute, 500 requests/day
        </p>
      </div>

      {/* Security notice */}
      <div className="mt-3 text-sm">
        <p className="font-medium" style={{
          color: 'var(--text-primary)',
          fontFamily: 'var(--font-diablo)',
          textTransform: 'uppercase',
          fontSize: '0.9rem',
          letterSpacing: '0.5px',
          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
        }}>Arcane Security Ward</p>
        <p style={{
          color: 'var(--text-secondary)',
          fontFamily: 'var(--font-diablo)',
          textTransform: 'uppercase',
          fontSize: '0.85rem',
          letterSpacing: '0.5px',
          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
        }}>Your arcane secrets (API key) are bound to your local realm and never traverse our domains.</p>
      </div>
    </div>
  );
};

export default ApiKeyInput;
