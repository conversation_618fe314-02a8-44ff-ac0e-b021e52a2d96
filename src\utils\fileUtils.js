import { FILE_CONFIG, MESSAGES } from './constants.js';

/**
 * Validates uploaded file
 * @param {File} file - The uploaded file
 * @returns {Object} - Validation result with isValid and error message
 */
export const validateFile = (file) => {
  if (!file) {
    return { isValid: false, error: 'No file selected' };
  }

  // Check file size
  if (file.size > FILE_CONFIG.MAX_FILE_SIZE) {
    return { isValid: false, error: MESSAGES.FILE_TOO_LARGE };
  }

  // Check file extension
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
  if (!FILE_CONFIG.ALLOWED_EXTENSIONS.includes(fileExtension)) {
    return { isValid: false, error: MESSAGES.INVALID_FILE_TYPE };
  }

  return { isValid: true, error: null };
};

/**
 * Reads file content as text
 * @param {File} file - The file to read
 * @returns {Promise<string>} - File content as string
 */
export const readFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      resolve(event.target.result);
    };

    reader.onerror = (error) => {
      reject(new Error('Failed to read file: ' + error.message));
    };

    reader.readAsText(file, 'UTF-8');
  });
};

/**
 * Downloads text content as a file
 * @param {string} content - Text content to download
 * @param {string} filename - Name for the downloaded file
 */
export const downloadTextFile = (content, filename) => {
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();

  // Cleanup
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Generates filename for translated document
 * @param {string} originalFilename - Original file name
 * @param {string} targetLanguage - Target language code
 * @returns {string} - Generated filename
 */
export const generateTranslatedFilename = (originalFilename, targetLanguage) => {
  const nameWithoutExt = originalFilename.replace(/\.[^/.]+$/, '');
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  return `${nameWithoutExt}_translated_${targetLanguage}_${timestamp}.txt`;
};

/**
 * Formats file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Estimates reading time for text
 * @param {string} text - Text content
 * @returns {string} - Estimated reading time
 */
export const estimateReadingTime = (text) => {
  const wordsPerMinute = 200;
  const words = text.trim().split(/\s+/).length;
  const minutes = Math.ceil(words / wordsPerMinute);

  if (minutes < 1) return 'Less than 1 minute';
  if (minutes === 1) return '1 minute';
  return `${minutes} minutes`;
};

/**
 * Counts words in text
 * @param {string} text - Text content
 * @returns {number} - Word count
 */
export const countWords = (text) => {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
};

/**
 * Counts characters in text
 * @param {string} text - Text content
 * @returns {number} - Character count
 */
export const countCharacters = (text) => {
  return text.length;
};

/**
 * Removes chunk markers from translated text
 * @param {string} text - Text with chunk markers
 * @returns {string} - Text without chunk markers
 */
export const removeChunkMarkers = (text) => {
  if (!text) return '';

  // Remove the ***** markers and normalize spacing
  return text
    .replace(/\n\n\*{5}\n\n/g, '\n\n')
    .replace(/\*{5}/g, '')
    .replace(/\n{3,}/g, '\n\n')
    .trim();
};
