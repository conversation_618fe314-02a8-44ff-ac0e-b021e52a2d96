/* Diablo-inspired component styles */

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: '<PERSON>in<PERSON>', serif;
  color: var(--text-primary);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

label {
  font-family: 'Crimson Text', serif;
  color: var(--text-secondary);
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
}

/* File Upload Animations */
.file-upload-area {
  transition: all 0.3s ease;
  background: linear-gradient(145deg, var(--background-secondary), var(--surface-darker));
  border: 2px dashed var(--border-muted);
}

.file-upload-area:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(139, 0, 0, 0.2);
  border-color: var(--border-primary);
}

/* Progress Bar Animations */
@keyframes progressPulse {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 10px rgba(139, 0, 0, 0.6);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 20px rgba(139, 0, 0, 0.8);
  }
}

.progress-bar-active {
  animation: progressPulse 2s ease-in-out infinite;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-color));
  background-size: 200% 100%;
  animation: progressPulse 2s ease-in-out infinite, progressShine 3s linear infinite;
}

@keyframes progressShine {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Button Hover Effects */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

/* Card Hover Effects */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Language Selector Enhancements */
.language-pair-display {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

/* Status Messages */
.status-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading Spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* File Stats Display */
.file-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.file-stat-item {
  text-align: center;
  padding: 0.5rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 6px;
}

/* Progress Indicators */
.progress-step {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.progress-step-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 0.75rem;
  font-weight: bold;
}

.progress-step-icon.completed {
  background-color: #10b981;
  color: white;
}

.progress-step-icon.active {
  background-color: #3b82f6;
  color: white;
  animation: pulse 2s infinite;
}

.progress-step-icon.pending {
  background-color: #e5e7eb;
  color: #6b7280;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Spacing utilities */
.space-y-3 > * + * {
  margin-top: 0.75rem;
}

/* Responsive Design Enhancements */
@media (max-width: 640px) {
  .container {
    padding: 0 0.5rem;
  }

  .card {
    padding: 1rem;
    margin: 0.5rem 0;
  }

  .btn {
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
  }

  .file-stats {
    grid-template-columns: 1fr 1fr;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .card {
    background: rgba(30, 41, 59, 0.95);
    color: #f1f5f9;
    border: 1px solid rgba(71, 85, 105, 0.3);
  }

  .input, .select {
    background: #1e293b;
    color: #f1f5f9;
    border-color: #475569;
  }

  .input:focus, .select:focus {
    border-color: #3b82f6;
    background: #0f172a;
  }

  .file-stat-item {
    background: rgba(59, 130, 246, 0.2);
    color: #e2e8f0;
  }
}

/* Accessibility Enhancements */
.btn:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.input:focus-visible,
.select:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }

  .card {
    border: 2px solid #000;
  }

  .input, .select {
    border: 2px solid #000;
  }
}

/* Code styling for inline code */
code {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875em;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .btn::before {
    display: none;
  }
}
