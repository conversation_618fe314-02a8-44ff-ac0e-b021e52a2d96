# ETA (Estimated Time of Arrival) Feature

## 🎯 Что это такое?

ETA показывает оценочное время завершения перевода всего документа на основе скорости обработки уже переведенных частей.

## ⏱️ Как работает расчет?

1. **Засекается время начала** перевода
2. **Измеряется время** обработки каждого чанка
3. **Вычисляется средняя скорость** перевода
4. **Прогнозируется время** для оставшихся частей

### Формула расчета:
```
Среднее время на чанк = Общее время / Количество обработанных чанков
Оставшееся время = Среднее время × Количество оставшихся чанков
```

## 📊 Где отображается?

ETA показывается в компоненте прогресса во время перевода:
- 🕐 **"ETA: 5m 30s"** - если больше минуты
- 🕐 **"ETA: 45s"** - если меньше минуты

## 🔄 Обновление в реальном времени

- ETA пересчитывается после каждого переведенного чанка
- Становится точнее по мере обработки документа
- Учитывает фактическую скорость API Gemini

## 📈 Факторы, влияющие на точность:

### ✅ Повышают точность:
- Большое количество чанков
- Стабильная скорость API
- Однородный контент

### ⚠️ Могут снижать точность:
- Первые 1-2 чанка (мало данных)
- Переменная нагрузка на API
- Разная сложность частей текста

## 🛠️ Техническая реализация

### Файлы:
- `src/services/translationService.js` - логика расчета ETA
- `src/components/TranslationProgress.jsx` - отображение ETA
- `src/App.jsx` - передача данных ETA

### Ключевые функции:
```javascript
calculateETA(current, total) {
  // Расчет на основе elapsed time и прогресса
}

updateProgress(current, total, status) {
  // Обновление прогресса с ETA
}
```

## 💡 Советы по использованию

1. **Первые чанки**: ETA может быть неточным
2. **Середина процесса**: Наиболее точные оценки
3. **Большие файлы**: ETA особенно полезен
4. **Планирование**: Используйте для планирования времени

## 🎯 Преимущества

- **Прозрачность**: Пользователь знает, сколько ждать
- **Планирование**: Можно планировать другие задачи
- **UX**: Снижает тревожность ожидания
- **Контроль**: Понимание процесса перевода

ETA делает процесс перевода предсказуемым и комфортным!
