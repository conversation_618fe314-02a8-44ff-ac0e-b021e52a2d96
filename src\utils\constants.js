// Gemini API Configuration
export const GEMINI_CONFIG = {
  MODEL: 'gemini-2.0-flash-exp',
  API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent',

  // Rate Limits (Free Tier)
  RATE_LIMITS: {
    RPM: 10,        // Requests per minute
    TPM: 250000,    // Tokens per minute
    RPD: 500        // Requests per day
  },

  // Token Limits
  TOKEN_LIMITS: {
    INPUT_MAX: 1000000,     // Maximum input tokens
    OUTPUT_MAX: 8192,       // Practical output limit (documented as 65,536 but often ~8,000)
    CHUNK_SIZE: 6000        // Safe chunk size for translation
  }
};

// Supported Languages
export const LANGUAGES = [
  { code: 'auto', name: 'Auto-detect' },
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' },
  { code: 'it', name: 'Italian' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'ru', name: 'Russian' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'zh', name: 'Chinese (Simplified)' },
  { code: 'zh-TW', name: 'Chinese (Traditional)' },
  { code: 'ar', name: 'Arabic' },
  { code: 'hi', name: 'Hindi' },
  { code: 'th', name: 'Thai' },
  { code: 'vi', name: 'Vietnamese' },
  { code: 'nl', name: 'Dutch' },
  { code: 'sv', name: 'Swedish' },
  { code: 'da', name: 'Danish' },
  { code: 'no', name: 'Norwegian' },
  { code: 'fi', name: 'Finnish' },
  { code: 'pl', name: 'Polish' },
  { code: 'cs', name: 'Czech' },
  { code: 'hu', name: 'Hungarian' },
  { code: 'ro', name: 'Romanian' },
  { code: 'bg', name: 'Bulgarian' },
  { code: 'hr', name: 'Croatian' },
  { code: 'sk', name: 'Slovak' },
  { code: 'sl', name: 'Slovenian' },
  { code: 'et', name: 'Estonian' },
  { code: 'lv', name: 'Latvian' },
  { code: 'lt', name: 'Lithuanian' },
  { code: 'mt', name: 'Maltese' },
  { code: 'tr', name: 'Turkish' },
  { code: 'he', name: 'Hebrew' },
  { code: 'fa', name: 'Persian' },
  { code: 'ur', name: 'Urdu' },
  { code: 'bn', name: 'Bengali' },
  { code: 'ta', name: 'Tamil' },
  { code: 'te', name: 'Telugu' },
  { code: 'ml', name: 'Malayalam' },
  { code: 'kn', name: 'Kannada' },
  { code: 'gu', name: 'Gujarati' },
  { code: 'pa', name: 'Punjabi' },
  { code: 'mr', name: 'Marathi' },
  { code: 'ne', name: 'Nepali' },
  { code: 'si', name: 'Sinhala' },
  { code: 'my', name: 'Myanmar' },
  { code: 'km', name: 'Khmer' },
  { code: 'lo', name: 'Lao' },
  { code: 'ka', name: 'Georgian' },
  { code: 'am', name: 'Amharic' },
  { code: 'sw', name: 'Swahili' },
  { code: 'zu', name: 'Zulu' },
  { code: 'af', name: 'Afrikaans' },
  { code: 'is', name: 'Icelandic' },
  { code: 'ga', name: 'Irish' },
  { code: 'cy', name: 'Welsh' },
  { code: 'eu', name: 'Basque' },
  { code: 'ca', name: 'Catalan' },
  { code: 'gl', name: 'Galician' },
  { code: 'lb', name: 'Luxembourgish' }
];

// Literary Content Detection Patterns
export const LITERARY_PATTERNS = [
  /chapter\s+\d+/i,
  /once\s+upon\s+a\s+time/i,
  /in\s+the\s+beginning/i,
  /long\s+ago/i,
  /it\s+was\s+a\s+dark\s+and\s+stormy\s+night/i,
  /the\s+end/i,
  /epilogue/i,
  /prologue/i,
  /"[^"]*"\s+said\s+/i,
  /"[^"]*"\s+he\s+said/i,
  /"[^"]*"\s+she\s+said/i,
  /narrator/i,
  /protagonist/i,
  /character/i
];

// File Processing Configuration
export const FILE_CONFIG = {
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  ALLOWED_EXTENSIONS: ['.txt'],
  CHUNK_OVERLAP: 100, // Characters to overlap between chunks
  TESTING_MODE: true, // Process only first 3 chunks for testing
  MAX_CHUNKS_TESTING: 3,
  SHOW_CHUNK_MARKERS: true // Show ***** markers between chunks for testing
};

// UI Messages
export const MESSAGES = {
  FILE_TOO_LARGE: 'File size exceeds 50MB limit',
  INVALID_FILE_TYPE: 'Only .txt files are supported',
  API_KEY_REQUIRED: 'Gemini API key is required',
  LANGUAGES_REQUIRED: 'Please select both source and target languages',
  RATE_LIMIT_WARNING: 'Approaching daily request limit (500 requests)',
  TRANSLATION_COMPLETE: 'Translation completed successfully',
  TRANSLATION_ERROR: 'Translation failed. Please try again.',
  CHUNK_PROCESSING: 'Processing chunk {current} of {total}',
  TESTING_MODE_NOTICE: 'Testing mode: Processing only first 3 chunks'
};
