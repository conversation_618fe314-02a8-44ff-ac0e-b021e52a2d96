import React from 'react';
import { Download, FileText, Info, FileCheck } from 'lucide-react';
import { downloadTextFile, generateTranslatedFilename, removeChunkMarkers } from '../utils/fileUtils.js';
import { FILE_CONFIG } from '../utils/constants.js';

const DownloadButton = ({
  translatedText,
  originalFilename,
  targetLanguage,
  translationResult,
  disabled
}) => {
  const handleDownload = (removeMarkers = false) => {
    if (!translatedText || !originalFilename || !targetLanguage) {
      return;
    }

    const textToDownload = removeMarkers ? removeChunkMarkers(translatedText) : translatedText;
    const filename = generateTranslatedFilename(originalFilename, targetLanguage);
    downloadTextFile(textToDownload, filename);
  };

  if (!translatedText) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-4">
          <FileText className="h-5 w-5 text-green-500" />
          <h3 className="text-lg font-medium text-gray-900">
            Translation Ready
          </h3>
        </div>

        {/* Translation Info */}
        {translationResult && (
          <div className="mb-4 space-y-2 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>Content Type:</span>
              <span className="font-medium">
                {translationResult.isLiterary ? 'Literary/Creative' : 'Standard Text'}
              </span>
            </div>

            <div className="flex justify-between">
              <span>Chunks Processed:</span>
              <span className="font-medium">
                {translationResult.chunksProcessed}
                {translationResult.isTestingMode && ` of ${translationResult.totalChunks} (Testing Mode)`}
              </span>
            </div>

            {translationResult.isTestingMode && (
              <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-start space-x-2">
                  <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium">Testing Mode Active</p>
                    <p>Only the first 3 chunks were processed for validation. The full document contains {translationResult.totalChunks} chunks.</p>
                    <p className="mt-1"><strong>Note:</strong> Chunk boundaries are marked with <code>*****</code> for quality checking.</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Download Buttons */}
        <div className="space-y-3">
          <button
            onClick={() => handleDownload(false)}
            disabled={disabled}
            className="btn btn-primary w-full"
          >
            <Download className="h-4 w-4" />
            Download with Chunk Markers
            {FILE_CONFIG.SHOW_CHUNK_MARKERS && ' (*****)'}
          </button>

          {FILE_CONFIG.SHOW_CHUNK_MARKERS && (
            <button
              onClick={() => handleDownload(true)}
              disabled={disabled}
              className="btn btn-secondary w-full"
            >
              <FileCheck className="h-4 w-4" />
              Download Clean Version (No Markers)
            </button>
          )}
        </div>

        {/* Preview */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preview (first 500 characters)
          </label>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 text-sm text-gray-700 max-h-32 overflow-y-auto">
            {translatedText.slice(0, 500)}
            {translatedText.length > 500 && '...'}
          </div>
        </div>

        {/* Stats */}
        <div className="mt-4 grid grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <span className="font-medium">Characters:</span>{' '}
            {translatedText.length.toLocaleString()}
          </div>
          <div>
            <span className="font-medium">Words:</span>{' '}
            {translatedText.trim().split(/\s+/).length.toLocaleString()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadButton;
