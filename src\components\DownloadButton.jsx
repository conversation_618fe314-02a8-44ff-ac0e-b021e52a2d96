import React from 'react';
import { Download, FileText, Info } from 'lucide-react';
import { downloadTextFile, generateTranslatedFilename } from '../utils/fileUtils.js';

const DownloadButton = ({
  translatedText,
  originalFilename,
  targetLanguage,
  translationResult,
  disabled
}) => {
  const handleDownload = () => {
    if (!translatedText || !originalFilename || !targetLanguage) {
      return;
    }

    const filename = generateTranslatedFilename(originalFilename, targetLanguage);
    downloadTextFile(translatedText, filename);
  };

  if (!translatedText) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-4">
          <FileText className="h-5 w-5 text-green-500" />
          <h3 className="text-lg font-medium text-gray-900">
            Translation Ready
          </h3>
        </div>

        {/* Translation Info */}
        {translationResult && (
          <div className="mb-4 space-y-2 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>Content Type:</span>
              <span className="font-medium">
                {translationResult.isLiterary ? 'Literary/Creative' : 'Standard Text'}
              </span>
            </div>

            <div className="flex justify-between">
              <span>Chunks Processed:</span>
              <span className="font-medium">
                {translationResult.chunksProcessed} of {translationResult.totalChunks}
              </span>
            </div>


          </div>
        )}

        {/* Download Button */}
        <button
          onClick={handleDownload}
          disabled={disabled}
          className="btn btn-primary w-full"
        >
          <Download className="h-4 w-4" />
          Download Translated Document
        </button>

        {/* Preview */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preview (first 500 characters)
          </label>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 text-sm text-gray-700 max-h-32 overflow-y-auto">
            {translatedText.slice(0, 500)}
            {translatedText.length > 500 && '...'}
          </div>
        </div>

        {/* Stats */}
        <div className="mt-4 grid grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <span className="font-medium">Characters:</span>{' '}
            {translatedText.length.toLocaleString()}
          </div>
          <div>
            <span className="font-medium">Words:</span>{' '}
            {translatedText.trim().split(/\s+/).length.toLocaleString()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadButton;
