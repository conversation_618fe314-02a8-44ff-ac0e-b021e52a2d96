import { translateWithG<PERSON>ini, getRateLimiter } from './geminiApi.js';
import {
  splitTextIntoChunks,
  detectLiteraryContent,
  reconstructText,
  prepareTestingChunks
} from './textProcessor.js';
import { FILE_CONFIG, MESSAGES } from '../utils/constants.js';

/**
 * Main translation service that orchestrates the entire translation process
 */
export class TranslationService {
  constructor() {
    this.isTranslating = false;
    this.currentProgress = 0;
    this.totalChunks = 0;
    this.onProgress = null;
    this.onStatusUpdate = null;
  }

  /**
   * Sets progress callback
   * @param {Function} callback - Progress callback function
   */
  setProgressCallback(callback) {
    this.onProgress = callback;
  }

  /**
   * Sets status update callback
   * @param {Function} callback - Status update callback function
   */
  setStatusCallback(callback) {
    this.onStatusUpdate = callback;
  }

  /**
   * Updates progress and calls callback
   * @param {number} current - Current chunk number
   * @param {number} total - Total chunks
   * @param {string} status - Status message
   */
  updateProgress(current, total, status) {
    this.currentProgress = current;
    this.totalChunks = total;

    if (this.onProgress) {
      this.onProgress({
        current,
        total,
        percentage: Math.round((current / total) * 100),
        status
      });
    }

    if (this.onStatusUpdate) {
      this.onStatusUpdate(status);
    }
  }

  /**
   * Validates translation parameters
   * @param {Object} params - Translation parameters
   * @returns {Object} - Validation result
   */
  validateParameters(params) {
    const { text, sourceLanguage, targetLanguage, apiKey } = params;

    if (!text || text.trim().length === 0) {
      return { isValid: false, error: 'No text to translate' };
    }

    if (!apiKey || apiKey.trim().length === 0) {
      return { isValid: false, error: MESSAGES.API_KEY_REQUIRED };
    }

    if (!sourceLanguage || !targetLanguage) {
      return { isValid: false, error: MESSAGES.LANGUAGES_REQUIRED };
    }

    if (sourceLanguage === targetLanguage) {
      return { isValid: false, error: 'Source and target languages cannot be the same' };
    }

    return { isValid: true, error: null };
  }

  /**
   * Translates text with chunking and progress tracking
   * @param {Object} params - Translation parameters
   * @returns {Promise<Object>} - Translation result
   */
  async translateText(params) {
    const { text, sourceLanguage, targetLanguage, apiKey } = params;

    // Validate parameters
    const validation = this.validateParameters(params);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    if (this.isTranslating) {
      throw new Error('Translation already in progress');
    }

    this.isTranslating = true;

    try {
      // Check rate limits and show warnings
      const rateLimiter = getRateLimiter();
      const warning = rateLimiter.getWarning();
      if (warning && this.onStatusUpdate) {
        this.onStatusUpdate(warning);
      }

      // Detect if content is literary
      this.updateProgress(0, 1, 'Analyzing content...');
      const isLiterary = detectLiteraryContent(text);

      if (isLiterary && this.onStatusUpdate) {
        this.onStatusUpdate('Literary content detected - using enhanced translation mode');
      }

      // Split text into chunks
      this.updateProgress(0, 1, 'Preparing text chunks...');
      const allChunks = splitTextIntoChunks(text);

      // Apply testing mode limitation
      const chunks = prepareTestingChunks(allChunks);

      if (FILE_CONFIG.TESTING_MODE && this.onStatusUpdate) {
        this.onStatusUpdate(MESSAGES.TESTING_MODE_NOTICE);
      }

      this.totalChunks = chunks.length;

      if (chunks.length === 0) {
        throw new Error('No valid text chunks to translate');
      }

      // Translate each chunk
      const translatedChunks = [];

      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const chunkNumber = i + 1;

        this.updateProgress(
          i,
          chunks.length,
          MESSAGES.CHUNK_PROCESSING.replace('{current}', chunkNumber).replace('{total}', chunks.length)
        );

        try {
          // Add delay between requests to respect rate limits
          if (i > 0) {
            await this.delay(6000); // 6 seconds between requests (10 RPM = 6 seconds)
          }

          const translatedChunk = await translateWithGemini(
            chunk,
            sourceLanguage,
            targetLanguage,
            apiKey,
            isLiterary
          );

          translatedChunks.push(translatedChunk);

        } catch (error) {
          console.error(`Error translating chunk ${chunkNumber}:`, error);

          // For testing mode, we can be more lenient with errors
          if (FILE_CONFIG.TESTING_MODE) {
            translatedChunks.push(`[Translation failed for chunk ${chunkNumber}: ${error.message}]`);
            if (this.onStatusUpdate) {
              this.onStatusUpdate(`Warning: Chunk ${chunkNumber} failed - ${error.message}`);
            }
          } else {
            throw new Error(`Failed to translate chunk ${chunkNumber}: ${error.message}`);
          }
        }
      }

      // Reconstruct the translated text
      this.updateProgress(chunks.length, chunks.length, 'Reconstructing translated text...');
      const translatedText = reconstructText(translatedChunks, isLiterary, FILE_CONFIG.SHOW_CHUNK_MARKERS);

      this.updateProgress(chunks.length, chunks.length, MESSAGES.TRANSLATION_COMPLETE);

      return {
        success: true,
        translatedText,
        isLiterary,
        chunksProcessed: chunks.length,
        totalChunks: allChunks.length,
        isTestingMode: FILE_CONFIG.TESTING_MODE
      };

    } catch (error) {
      console.error('Translation service error:', error);
      if (this.onStatusUpdate) {
        this.onStatusUpdate(`Error: ${error.message}`);
      }
      throw error;
    } finally {
      this.isTranslating = false;
    }
  }

  /**
   * Cancels ongoing translation
   */
  cancelTranslation() {
    this.isTranslating = false;
    if (this.onStatusUpdate) {
      this.onStatusUpdate('Translation cancelled');
    }
  }

  /**
   * Gets current translation status
   * @returns {Object} - Current status
   */
  getStatus() {
    return {
      isTranslating: this.isTranslating,
      currentProgress: this.currentProgress,
      totalChunks: this.totalChunks,
      percentage: this.totalChunks > 0 ? Math.round((this.currentProgress / this.totalChunks) * 100) : 0
    };
  }

  /**
   * Utility function to add delay
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} - Promise that resolves after delay
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const translationService = new TranslationService();
